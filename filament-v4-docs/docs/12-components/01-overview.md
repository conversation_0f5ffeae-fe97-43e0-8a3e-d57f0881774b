---
title: Overview
---

## Introduction

Filament packages consume a set of core components that aim to provide a consistent and maintainable foundation for all interfaces. Some of these components are also available for use in your own applications and Filament plugins.

## Package components

The various packages in the Filament project can be used outside of a panel:

- [Action](action)
- [Form](form)
- [Infolist](infolist)
- [Notifications](notifications)
- [Schema](schema)
- [Table](table)
- [Widget](widget)

## Blade components

Aside from the core packages, all Filament projects can also consume the Blade components that Filament uses internally:

- [Avatar](avatar)
- [Badge](badge)
- [Button](button)
- [Breadcrumbs](breadcrumbs)
- [Checkbox](checkbox)
- [Dropdown](dropdown)
- [Fieldset](fieldset)
- [Icon button](icon-button)
- [Input](input)
- [Input wrapper](input-wrapper)
- [Link](link)
- [Loading indicator](loading-indicator)
- [Modal](modal)
- [Pagination](pagination)
- [Section](section)
- [Select](select)
- [Tabs](tabs)

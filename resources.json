{"resources": [{"name": "Country", "navigation_group": "Master data", "fields": [{"name": "name", "type": "text", "field": "Translatable", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": true, "priceable": false, "instructions": null}, {"name": "code", "type": "string", "field": "TextInput", "repeater_type": null, "required": false, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}], "relations": [{"name": "cities", "type": "hasMany", "related_model": "City", "form_field": "relation_manager", "repeater_type": null, "instructions": null}, {"name": "airports", "type": "hasMany", "related_model": "Airport", "form_field": "relation_manager", "repeater_type": null, "instructions": null}, {"name": "seaports", "type": "hasMany", "related_model": "Seaport", "form_field": "relation_manager", "repeater_type": null, "instructions": null}]}, {"name": "City", "navigation_group": "Master data", "fields": [{"name": "name", "type": "text", "field": "Translatable", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "code", "type": "string", "field": "TextInput", "repeater_type": null, "required": false, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "country_id", "type": "foreignId", "field": "BelongsToSelect", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": true, "preload": true, "translateable": false, "priceable": false, "instructions": null}], "relations": [{"name": "country", "type": "belongsTo", "related_model": "Country", "form_field": "belongs_to_select", "repeater_type": null, "instructions": null}, {"name": "airports", "type": "hasMany", "related_model": "Airport", "form_field": "relation_manager", "repeater_type": null, "instructions": null}, {"name": "seaports", "type": "hasMany", "related_model": "Seaport", "form_field": "relation_manager", "repeater_type": null, "instructions": null}]}, {"name": "Seaport", "navigation_group": "Master data", "fields": [{"name": "name", "type": "string", "field": "TextInput", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "un_locode", "type": "string", "field": "TextInput", "repeater_type": null, "required": false, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "country_id", "type": "foreignId", "field": "BelongsToSelect", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "city_id", "type": "foreignId", "field": "BelongsToSelect", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}], "relations": [{"name": "country", "type": "belongsTo", "related_model": "Country", "form_field": "belongs_to_select", "repeater_type": null, "instructions": null}, {"name": "city", "type": "belongsTo", "related_model": "City", "form_field": "belongs_to_select", "repeater_type": null, "instructions": null}]}, {"name": "Airport", "navigation_group": "Master data", "fields": [{"name": "name", "type": "string", "field": "TextInput", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "iata_code", "type": "string", "field": "TextInput", "repeater_type": null, "required": false, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "icao_code", "type": "string", "field": "TextInput", "repeater_type": null, "required": false, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "un_locode", "type": "string", "field": "TextInput", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "country_id", "type": "foreignId", "field": "BelongsToSelect", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "city_id", "type": "foreignId", "field": "BelongsToSelect", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}], "relations": [{"name": "country", "type": "belongsTo", "related_model": "Country", "form_field": "belongs_to_select", "repeater_type": null, "instructions": null}, {"name": "city", "type": "belongsTo", "related_model": "City", "form_field": "belongs_to_select", "repeater_type": null, "instructions": null}]}]}